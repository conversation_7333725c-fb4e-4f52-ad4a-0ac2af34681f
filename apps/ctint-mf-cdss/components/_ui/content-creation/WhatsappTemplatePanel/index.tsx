import React, { useState, useMemo, startTransition } from 'react';
import { Toaster } from '@cdss-modules/design-system/components/_ui/Toast/toaster';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import { useRoute<PERSON>and<PERSON> } from '@cdss-modules/design-system';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { Pagination } from '@cdss-modules/design-system/components/_ui/Pagination';
import { MoreHorizontal } from 'lucide-react';

// 模板数据类型定义
interface TemplateData {
  id: string;
  name: string;
  category: string;
  status: 'draft' | 'published';
  language: string;
  createdBy: string;
  createdDate: string;
}

// 状态标签组件
const StatusBadge: React.FC<{ status: 'draft' | 'published' }> = ({
  status,
}) => {
  const statusStyles = {
    draft: 'bg-gray-100 text-gray-600 border-gray-300',
    published: 'bg-green-100 text-green-600 border-green-300',
  };

  return (
    <span
      className={`px-2 py-1 text-xs rounded border ${statusStyles[status]}`}
    >
      {status === 'draft' ? 'Draft' : 'Published'}
    </span>
  );
};

// 语言标签组件
const LanguageBadge: React.FC<{ language: string }> = ({ language }) => {
  const getLanguageColor = (lang: string) => {
    switch (lang) {
      case 'en':
        return 'bg-blue-100 text-blue-600';
      case 'zh-CN':
        return 'bg-orange-100 text-orange-600';
      case 'zh-HK':
        return 'bg-purple-100 text-purple-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  return (
    <span className={`px-2 py-1 text-xs rounded ${getLanguageColor(language)}`}>
      {language}
    </span>
  );
};

// 操作列组件
const ActionColumn: React.FC<{ row: TemplateData }> = ({ row: _row }) => {
  return (
    <div className="flex items-center justify-center">
      <Button
        variant="blank"
        size="s"
        asSquare
        className="hover:bg-gray-100"
      >
        <MoreHorizontal size={16} />
      </Button>
    </div>
  );
};

// 主要组件实现
const WhatsappTemplatePanelBody = (_props: { parentId: string }) => {
  const [_searchParams] = useSearchParams();
  const { basePath: _basePath } = useRouteHandler();

  // 状态管理
  const [selectedAccount, setSelectedAccount] = useState('');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [_sortConfig, _setSortConfig] = useState<{
    column: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  // 模拟数据 - 实际应该从API获取
  const mockData: TemplateData[] = useMemo(
    () => [
      {
        id: '1',
        name: 'Salutation',
        category: 'Marketing',
        status: 'draft',
        language: 'zh-CN',
        createdBy: 'Wing',
        createdDate: '2025/4/1 23:12:00',
      },
      {
        id: '2',
        name: 'Template Name',
        category: 'Marketing',
        status: 'draft',
        language: 'en',
        createdBy: 'Alex',
        createdDate: '2025/4/1 23:12:00',
      },
      {
        id: '3',
        name: 'Template Name',
        category: 'Marketing',
        status: 'draft',
        language: 'zh-HK',
        createdBy: 'Jack',
        createdDate: '2025/4/1 23:12:00',
      },
      {
        id: '4',
        name: 'Template Name',
        category: 'Marketing',
        status: 'draft',
        language: 'zh-CN',
        createdBy: 'Ben',
        createdDate: '2025/4/1 23:12:00',
      },
      {
        id: '5',
        name: 'Template Name',
        category: 'Marketing',
        status: 'published',
        language: 'zh-CN',
        createdBy: 'Wing',
        createdDate: '2025/4/1 23:12:00',
      },
    ],
    []
  );

  // 表格列定义
  const columns: ColumnDef<TemplateData>[] = useMemo(
    () => [
      {
        accessorKey: 'name',
        header: 'Template Name',
        size: 150,
        cell: ({ row }) => (
          <div
            className="w-[150px] truncate font-medium text-gray-900"
            title={row.getValue('name')}
          >
            {row.getValue('name')}
          </div>
        ),
      },
      {
        accessorKey: 'category',
        header: 'Category',
        size: 120,
        cell: ({ row }) => (
          <div className="text-gray-700">{row.getValue('category')}</div>
        ),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        size: 100,
        cell: ({ row }) => (
          <div className="flex justify-start">
            <StatusBadge status={row.getValue('status')} />
          </div>
        ),
      },
      {
        accessorKey: 'language',
        header: 'Language',
        size: 100,
        cell: ({ row }) => (
          <div className="flex justify-start">
            <LanguageBadge language={row.getValue('language')} />
          </div>
        ),
      },
      {
        accessorKey: 'createdBy',
        header: 'Created by',
        size: 120,
        cell: ({ row }) => (
          <div className="text-gray-700">{row.getValue('createdBy')}</div>
        ),
      },
      {
        accessorKey: 'createdDate',
        header: 'Created_date',
        size: 150,
        cell: ({ row }) => (
          <div className="text-gray-600 text-sm">
            {row.getValue('createdDate')}
          </div>
        ),
      },
      {
        id: 'actions',
        header: 'Action',
        size: 80,
        cell: ({ row }) => (
          <div className="flex justify-center">
            <ActionColumn row={row.original} />
          </div>
        ),
      },
    ],
    []
  );

  // 搜索处理
  const handleSearch = (value: string | number) => {
    startTransition(() => {
      setSearchKeyword(String(value));
      setCurrentPage(1);
    });
  };

  // 过滤数据
  const filteredData = useMemo(() => {
    let filtered = mockData;

    if (searchKeyword) {
      filtered = filtered.filter(
        (item) =>
          item.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
          item.category.toLowerCase().includes(searchKeyword.toLowerCase()) ||
          item.createdBy.toLowerCase().includes(searchKeyword.toLowerCase())
      );
    }

    return filtered;
  }, [searchKeyword, mockData]);

  // 分页数据
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredData.length / pageSize);

  return (
    <div className="w-full h-full p-4 flex flex-col">
      {/* 头部操作区 */}
      <div className="flex gap-2 flex-shrink-0 mb-4">
        <div className="flex items-center gap-4 flex-1">
          {/* WhatsApp账户选择 */}
          <div className="flex items-center">
            <Select
              placeholder="Select WhatsApp Account"
              mode="single"
              options={[
                {
                  id: 'account1',
                  label: 'WhatsApp Account 1',
                  value: 'account1',
                },
                {
                  id: 'account2',
                  label: 'WhatsApp Account 2',
                  value: 'account2',
                },
              ]}
              value={selectedAccount}
              onChange={setSelectedAccount}
              triggerClassName="w-[220px]"
            />
          </div>

          {/* 搜索框 */}
          <div className="flex items-center">
            <Input
              placeholder="Search..."
              value={searchKeyword}
              onChange={handleSearch}
              beforeIcon={<Icon name="search" />}
              className="w-[250px]"
              containerClassName="w-[250px]"
            />
          </div>
        </div>

        {/* 右侧按钮 */}
        <div className="flex items-center gap-3 ml-4">
          <Button
            variant="secondary"
            size="m"
            className="whitespace-nowrap"
          >
            Go to flows
          </Button>
          <Button
            variant="primary"
            size="m"
            className="whitespace-nowrap"
          >
            Create Template
          </Button>
        </div>
      </div>

      {/* 表格和分页容器 - 使用flex布局 */}
      <div className="flex-1 min-h-0 flex flex-col">
        {/* 表格容器 - 使用flex-1撑满 */}
        <div
          className="flex-1 min-h-0 overflow-hidden"
          style={{
            height: 'calc(100% - 100px)', // Reserve space for pagination (approximately 80px)
          }}
        >
          <DataTable
            data={paginatedData}
            columns={columns}
            loading={false}
            emptyMessage="No templates found"
          />
        </div>
      </div>

      {/* 分页容器 - 使用flex-none固定在底部 */}
      {Math.ceil(filteredData.length / pageSize) > 0 && (
        <div
          className="flex-shrink-0 mt-4 pt-4 border-t border-gray-200"
          style={{ height: '100px' }} // Fixed height for pagination
        >
          <Pagination
            total={totalPages}
            current={currentPage}
            onChange={setCurrentPage}
            totalCount={filteredData.length}
            perPage={pageSize}
            handlePerPageSetter={(newPageSize) => {
              setPageSize(parseInt(String(newPageSize)));
              setCurrentPage(1);
            }}
          />
        </div>
      )}
    </div>
  );
};

// Create a client
const queryClient = new QueryClient();

export const WhatsappTemplatePanel = (props: { parentId: string }) => (
  <QueryClientProvider client={queryClient}>
    <WhatsappTemplatePanelBody parentId={props.parentId} />
    <Toaster />
  </QueryClientProvider>
);

export default WhatsappTemplatePanel;
