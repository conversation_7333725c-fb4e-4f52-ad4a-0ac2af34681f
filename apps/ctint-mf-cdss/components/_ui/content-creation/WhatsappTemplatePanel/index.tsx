import React, { useState, useMemo, startTransition, useCallback } from 'react';
import { Toaster } from '@cdss-modules/design-system/components/_ui/Toast/toaster';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import { useRouteHandler } from '@cdss-modules/design-system';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { Pagination } from '@cdss-modules/design-system/components/_ui/Pagination';
import { MoreH<PERSON>zon<PERSON>, <PERSON>, Edit, Trash2 } from 'lucide-react';
import WhatsAppTemplateDetail from './Detail';

// 模板数据类型定义
interface TemplateData {
  id: string;
  name: string;
  category: string;
  status: 'draft' | 'published';
  language: string;
  createdBy: string;
  createdDate: string;
}

// 状态标签组件
const StatusBadge: React.FC<{ status: 'draft' | 'published' }> = ({
  status,
}) => {
  const statusStyles = {
    draft: 'bg-gray-100 text-gray-600 border-gray-300',
    published: 'bg-green-100 text-green-600 border-green-300',
  };

  return (
    <span
      className={`px-2 py-1 text-xs rounded border ${statusStyles[status]}`}
    >
      {status === 'draft' ? 'Draft' : 'Published'}
    </span>
  );
};

// 语言标签组件
const LanguageBadge: React.FC<{ language: string }> = ({ language }) => {
  const getLanguageColor = (lang: string) => {
    switch (lang) {
      case 'en':
        return 'bg-blue-100 text-blue-600';
      case 'zh-CN':
        return 'bg-orange-100 text-orange-600';
      case 'zh-HK':
        return 'bg-purple-100 text-purple-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  return (
    <span className={`px-2 py-1 text-xs rounded ${getLanguageColor(language)}`}>
      {language}
    </span>
  );
};

// 操作列组件
const ActionColumn: React.FC<{
  row: TemplateData;
  onView: (template: TemplateData) => void;
  onEdit: (template: TemplateData) => void;
  onDelete: (template: TemplateData) => void;
}> = ({ row, onView, onEdit, onDelete }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // 点击外部关闭下拉菜单
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDropdown]);

  return (
    <div
      className="relative flex items-center justify-center"
      ref={dropdownRef}
    >
      <button
        className="p-2 hover:bg-gray-100 rounded-md transition-colors"
        onClick={(e: React.MouseEvent) => {
          e.stopPropagation(); // 防止触发行点击事件
          console.log(
            'Action button clicked, current showDropdown:',
            showDropdown
          );
          setShowDropdown(!showDropdown);
        }}
      >
        <MoreHorizontal size={16} />
      </button>

      {showDropdown && (
        <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-[120px]">
          <button
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
            onClick={(e: React.MouseEvent) => {
              e.stopPropagation();
              console.log('View clicked for:', row);
              onView(row);
              setShowDropdown(false);
            }}
          >
            <Eye size={14} />
            View
          </button>
          <button
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
            onClick={(e: React.MouseEvent) => {
              e.stopPropagation();
              console.log('Edit clicked for:', row);
              onEdit(row);
              setShowDropdown(false);
            }}
          >
            <Edit size={14} />
            Edit
          </button>
          <button
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 text-red-600"
            onClick={(e: React.MouseEvent) => {
              e.stopPropagation();
              console.log('Delete clicked for:', row);
              onDelete(row);
              setShowDropdown(false);
            }}
          >
            <Trash2 size={14} />
            Delete
          </button>
        </div>
      )}

      {/* 调试信息 */}
      {showDropdown && (
        <div className="absolute right-0 top-16 bg-red-100 border border-red-300 rounded p-2 text-xs z-50">
          Debug: Dropdown is visible
        </div>
      )}
    </div>
  );
};

// 主要组件实现
const WhatsappTemplatePanelBody = (_props: { parentId: string }) => {
  const [_searchParams] = useSearchParams();
  const { basePath: _basePath } = useRouteHandler();

  // 状态管理
  const [selectedAccount, setSelectedAccount] = useState('');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [_sortConfig, _setSortConfig] = useState<{
    column: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  // 详情页面状态管理
  const [showDetail, setShowDetail] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateData | null>(
    null
  );
  const [editable, setEditable] = useState(false);

  // 处理操作函数
  const handleView = useCallback((template: TemplateData) => {
    setSelectedTemplate(template);
    setEditable(false);
    setShowDetail(true);
  }, []);

  const handleEdit = useCallback((template: TemplateData) => {
    setSelectedTemplate(template);
    setEditable(true);
    setShowDetail(true);
  }, []);

  const handleDelete = useCallback((template: TemplateData) => {
    console.log('Delete template:', template);
    // 这里可以添加删除确认弹窗和API调用
  }, []);

  const handleCreateNew = useCallback(() => {
    setSelectedTemplate(null);
    setEditable(true);
    setShowDetail(true);
  }, []);

  const handleBackToList = useCallback(() => {
    setShowDetail(false);
    setSelectedTemplate(null);
    setEditable(false);
  }, []);

  // 模拟数据 - 实际应该从API获取
  const mockData: TemplateData[] = useMemo(
    () => [
      {
        id: '1',
        name: 'Salutation',
        category: 'Marketing',
        status: 'draft',
        language: 'zh-CN',
        createdBy: 'Wing',
        createdDate: '2025/4/1 23:12:00',
      },
      {
        id: '2',
        name: 'Template Name',
        category: 'Marketing',
        status: 'draft',
        language: 'en',
        createdBy: 'Alex',
        createdDate: '2025/4/1 23:12:00',
      },
      {
        id: '3',
        name: 'Template Name',
        category: 'Marketing',
        status: 'draft',
        language: 'zh-HK',
        createdBy: 'Jack',
        createdDate: '2025/4/1 23:12:00',
      },
      {
        id: '4',
        name: 'Template Name',
        category: 'Marketing',
        status: 'draft',
        language: 'zh-CN',
        createdBy: 'Ben',
        createdDate: '2025/4/1 23:12:00',
      },
      {
        id: '5',
        name: 'Template Name',
        category: 'Marketing',
        status: 'published',
        language: 'zh-CN',
        createdBy: 'Wing',
        createdDate: '2025/4/1 23:12:00',
      },
    ],
    []
  );

  // 搜索处理
  const handleSearch = (value: string | number) => {
    startTransition(() => {
      setSearchKeyword(String(value));
      setCurrentPage(1);
    });
  };

  // 过滤数据
  const filteredData = useMemo(() => {
    let filtered = mockData;

    if (searchKeyword) {
      filtered = filtered.filter(
        (item) =>
          item.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
          item.category.toLowerCase().includes(searchKeyword.toLowerCase()) ||
          item.createdBy.toLowerCase().includes(searchKeyword.toLowerCase())
      );
    }

    return filtered;
  }, [searchKeyword, mockData]);

  // 分页数据
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredData.length / pageSize);

  // 表格列定义 - 移到处理函数之后
  const columns: ColumnDef<TemplateData>[] = useMemo(
    () => [
      {
        accessorKey: 'name',
        header: 'Template Name',
        size: 150,
        cell: ({ row }) => (
          <div
            className="w-[150px] truncate font-medium text-gray-900 cursor-pointer hover:text-blue-600"
            title={row.getValue('name')}
            onClick={() => handleView(row.original)}
          >
            {row.getValue('name')}
          </div>
        ),
      },
      {
        accessorKey: 'category',
        header: 'Category',
        size: 120,
        cell: ({ row }) => (
          <div
            className="text-gray-700 cursor-pointer"
            onClick={() => handleView(row.original)}
          >
            {row.getValue('category')}
          </div>
        ),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        size: 100,
        cell: ({ row }) => (
          <div
            className="flex justify-start cursor-pointer"
            onClick={() => handleView(row.original)}
          >
            <StatusBadge status={row.getValue('status')} />
          </div>
        ),
      },
      {
        accessorKey: 'language',
        header: 'Language',
        size: 100,
        cell: ({ row }) => (
          <div
            className="flex justify-start cursor-pointer"
            onClick={() => handleView(row.original)}
          >
            <LanguageBadge language={row.getValue('language')} />
          </div>
        ),
      },
      {
        accessorKey: 'createdBy',
        header: 'Created by',
        size: 120,
        cell: ({ row }) => (
          <div
            className="text-gray-700 cursor-pointer"
            onClick={() => handleView(row.original)}
          >
            {row.getValue('createdBy')}
          </div>
        ),
      },
      {
        accessorKey: 'createdDate',
        header: 'Created_date',
        size: 150,
        cell: ({ row }) => (
          <div
            className="text-gray-600 text-sm cursor-pointer"
            onClick={() => handleView(row.original)}
          >
            {row.getValue('createdDate')}
          </div>
        ),
      },
      {
        id: 'actions',
        header: 'Action',
        size: 80,
        cell: ({ row }) => (
          <div className="flex justify-center">
            <ActionColumn
              row={row.original}
              onView={handleView}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          </div>
        ),
      },
    ],
    [handleView, handleEdit, handleDelete]
  );

  // 如果显示详情页面，渲染详情组件
  if (showDetail) {
    return (
      <WhatsAppTemplateDetail
        editable={editable}
        setEditMode={handleBackToList}
        selectedTemplate={
          selectedTemplate
            ? {
                id: selectedTemplate.id,
                name: selectedTemplate.name,
                channelType: 'whatsapp',
                createBy: selectedTemplate.createdBy,
                lastUpdateBy: selectedTemplate.createdBy,
                createDate: selectedTemplate.createdDate,
                lastUpdate: selectedTemplate.createdDate,
                sortorder: 0,
                status: selectedTemplate.status,
              }
            : undefined
        }
      />
    );
  }

  return (
    <div className="w-full h-full p-4 flex flex-col">
      {/* 头部操作区 */}
      <div className="flex gap-2 flex-shrink-0 mb-4">
        <div className="flex items-center gap-4 flex-1">
          {/* WhatsApp账户选择 */}
          <div className="flex items-center">
            <Select
              placeholder="Select WhatsApp Account"
              mode="single"
              options={[
                {
                  id: 'account1',
                  label: 'WhatsApp Account 1',
                  value: 'account1',
                },
                {
                  id: 'account2',
                  label: 'WhatsApp Account 2',
                  value: 'account2',
                },
              ]}
              value={selectedAccount}
              onChange={setSelectedAccount}
              triggerClassName="w-[220px]"
            />
          </div>

          {/* 搜索框 */}
          <div className="flex items-center">
            <Input
              placeholder="Search..."
              value={searchKeyword}
              onChange={handleSearch}
              beforeIcon={<Icon name="search" />}
              className="w-[250px]"
              containerClassName="w-[250px]"
            />
          </div>
        </div>

        {/* 右侧按钮 */}
        <div className="flex items-center gap-3 ml-4">
          <Button
            variant="secondary"
            size="m"
            className="whitespace-nowrap"
          >
            Go to flows
          </Button>
          <Button
            variant="primary"
            size="m"
            className="whitespace-nowrap"
            onClick={handleCreateNew}
          >
            Create Template
          </Button>
        </div>
      </div>

      {/* 表格和分页容器 - 使用flex布局 */}
      <div className="flex-1 min-h-0 flex flex-col">
        {/* 表格容器 - 使用flex-1撑满 */}
        <div
          className="flex-1 min-h-0 overflow-hidden"
          style={{
            height: 'calc(100% - 100px)', // Reserve space for pagination (approximately 80px)
          }}
        >
          <DataTable
            data={paginatedData}
            columns={columns}
            loading={false}
            emptyMessage="No templates found"
          />
        </div>
      </div>

      {/* 分页容器 - 使用flex-none固定在底部 */}
      {Math.ceil(filteredData.length / pageSize) > 0 && (
        <div
          className="flex-shrink-0 mt-4 pt-4 border-t border-gray-200"
          style={{ height: '100px' }} // Fixed height for pagination
        >
          <Pagination
            total={totalPages}
            current={currentPage}
            onChange={setCurrentPage}
            totalCount={filteredData.length}
            perPage={pageSize}
            handlePerPageSetter={(newPageSize) => {
              setPageSize(parseInt(String(newPageSize)));
              setCurrentPage(1);
            }}
          />
        </div>
      )}
    </div>
  );
};

// Create a client
const queryClient = new QueryClient();

export const WhatsappTemplatePanel = (props: { parentId: string }) => (
  <QueryClientProvider client={queryClient}>
    <WhatsappTemplatePanelBody parentId={props.parentId} />
    <Toaster />
  </QueryClientProvider>
);

export default WhatsappTemplatePanel;
