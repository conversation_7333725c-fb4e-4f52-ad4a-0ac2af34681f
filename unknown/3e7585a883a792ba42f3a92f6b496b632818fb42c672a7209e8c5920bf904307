import { ChevronLeft } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  submitTemplate,
  SubmitTemplateRequest,
  getTemplateDetail,
} from '../../../../../lib/content-creation/api';
import { useRouteHandler } from '@cdss-modules/design-system';
import { TemplateList } from '../../../../../types/content-creation/template';

// WhatsApp 模板组件类型定义
interface WhatsAppComponent {
  id: string;
  type: 'HEADER' | 'BODY' | 'FOOTER' | 'BUTTONS';
  format?: 'TEXT' | 'IMAGE';
  text?: string;
  buttons?: WhatsAppButton[];
}

interface WhatsAppButton {
  id: string;
  type: 'FLOW' | 'URL' | 'QUICK_REPLY' | 'PHONE_NUMBER';
  text: string;
  url?: string;
  phoneNumber?: string;
  flowName?: string;
}

const WhatsAppTemplateDetail = ({
  editable,
  setEditMode,
  selectedTemplate,
}: {
  editable: boolean;
  setEditMode?: (editMode: boolean) => void;
  selectedTemplate?: TemplateList;
}) => {
  const [templateName, setTemplateName] = React.useState('');
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const { basePath } = useRouteHandler();

  // 表单状态
  const [category, setCategory] = React.useState('');
  const [templateType, setTemplateType] = React.useState('');
  const [templateLanguage, setTemplateLanguage] = React.useState('');
  const [allowCategoryChange, setAllowCategoryChange] = React.useState(true);

  // 模板组件状态
  const [components, setComponents] = React.useState<WhatsAppComponent[]>([]);
  const [selectedSection, setSelectedSection] = React.useState<
    'Header' | 'Body' | 'Footer' | 'Buttons'
  >('Header');

  // UI 状态
  const [showTooltip, setShowTooltip] = React.useState(false);
  const [tooltipPosition, setTooltipPosition] = React.useState<{
    x: number;
    y: number;
    showBelow?: boolean;
    arrowOffset?: number;
  } | null>(null);
  const helpIconRef = React.useRef<HTMLDivElement>(null);
  const [showDiscardPopup, setShowDiscardPopup] = React.useState(false);
  const [showSubmitPopup, setShowSubmitPopup] = React.useState(false);

  // 验证状态
  const [validationErrors, setValidationErrors] = React.useState({
    templateName: false,
    category: false,
    templateType: false,
    templateLanguage: false,
  });

  // 加载状态
  const [isLoading, setIsLoading] = React.useState(false);
  const [templateId, setTemplateId] = React.useState<string | null>(null);

  // 获取模板详情
  const fetchTemplateDetail = React.useCallback(
    async (id: string) => {
      try {
        setIsLoading(true);
        console.log('Fetching template detail for ID:', id);

        const response = await getTemplateDetail(id, basePath);

        if (response.data.isSuccess && response.data.data) {
          const templateData = response.data.data;
          console.log('Template detail loaded:', templateData);

          // 填充表单数据
          setTemplateName(templateData.name);
          setTemplateLanguage(templateData.language);

          // 解析模板内容为组件
          // 这里需要根据实际的数据结构来解析
          // 暂时使用默认组件结构
          setComponents([
            {
              id: 'header-1',
              type: 'HEADER',
              format: 'IMAGE',
              text: '',
            },
            {
              id: 'body-1',
              type: 'BODY',
              text: templateData.content || '',
            },
            {
              id: 'footer-1',
              type: 'FOOTER',
              text: '',
            },
          ]);
        }
      } catch (error) {
        console.error('Failed to fetch template detail:', error);
      } finally {
        setIsLoading(false);
      }
    },
    [basePath]
  );

  // 检查 selectedTemplate 中是否有模板 ID
  React.useEffect(() => {
    if (!selectedTemplate) return;
    const templateIdFromProps = selectedTemplate?.id;
    if (templateIdFromProps && templateIdFromProps !== templateId) {
      setTemplateId(templateIdFromProps);
      fetchTemplateDetail(templateIdFromProps);
    }
  }, [selectedTemplate, templateId, fetchTemplateDetail]);

  // 添加按钮
  const addButton = () => {
    const buttonsComponent = components.find((c) => c.type === 'BUTTONS');
    if (buttonsComponent) {
      const newButton: WhatsAppButton = {
        id: Date.now().toString(),
        type: 'FLOW',
        text: '',
        flowName: '',
      };

      setComponents((prev) =>
        prev.map((comp) =>
          comp.type === 'BUTTONS'
            ? { ...comp, buttons: [...(comp.buttons || []), newButton] }
            : comp
        )
      );
    } else {
      // 创建新的 BUTTONS 组件
      const newButtonsComponent: WhatsAppComponent = {
        id: 'buttons-1',
        type: 'BUTTONS',
        buttons: [
          {
            id: Date.now().toString(),
            type: 'FLOW',
            text: '',
            flowName: '',
          },
        ],
      };
      setComponents((prev) => [...prev, newButtonsComponent]);
    }
  };

  // 删除按钮
  const removeButton = (buttonId: string) => {
    setComponents((prev) =>
      prev.map((comp) =>
        comp.type === 'BUTTONS'
          ? {
              ...comp,
              buttons: comp.buttons?.filter((btn) => btn.id !== buttonId) || [],
            }
          : comp
      )
    );
  };

  // 更新按钮
  const updateButton = (
    buttonId: string,
    field: keyof WhatsAppButton,
    value: string
  ) => {
    setComponents((prev) =>
      prev.map((comp) =>
        comp.type === 'BUTTONS'
          ? {
              ...comp,
              buttons:
                comp.buttons?.map((btn) =>
                  btn.id === buttonId ? { ...btn, [field]: value } : btn
                ) || [],
            }
          : comp
      )
    );
  };

  // 更新组件内容
  const updateComponent = (
    componentId: string,
    field: keyof WhatsAppComponent,
    value: any
  ) => {
    setComponents((prev) =>
      prev.map((comp) =>
        comp.id === componentId ? { ...comp, [field]: value } : comp
      )
    );
  };

  // 处理丢弃确认
  const handleDiscardConfirm = () => {
    // 清空所有表单数据
    setTemplateName('');
    setCategory('');
    setTemplateType('');
    setTemplateLanguage('');
    setComponents([]);
    // 返回上一页
    setEditMode && setEditMode(false);
  };

  // 验证表单并显示提交弹窗
  const handleSubmitClick = () => {
    // 验证表单
    const errors = {
      templateName: !templateName || templateName.trim() === '',
      category: !category || category.trim() === '',
      templateType: !templateType || templateType.trim() === '',
      templateLanguage: !templateLanguage || templateLanguage.trim() === '',
    };

    setValidationErrors(errors);

    // 如果有验证错误，不显示弹窗
    if (
      errors.templateName ||
      errors.category ||
      errors.templateType ||
      errors.templateLanguage
    ) {
      return;
    }

    // 验证通过，显示确认弹窗
    setShowSubmitPopup(true);
  };

  // 处理提交确认
  const handleSubmitConfirm = async () => {
    try {
      // 从 searchParams 获取必要的参数
      const spaceId = searchParams.get('spaceId') || '';
      const channelType = 'whatsapp';
      const parentId = searchParams.get('id') || '';
      const id = selectedTemplate?.id;

      // 构建模板内容
      const templateContent = JSON.stringify({
        components: components,
      });

      // 构建 API 请求数据
      const requestData: SubmitTemplateRequest = {
        name: templateName,
        spaceId: spaceId,
        parentId: parentId,
        content: templateContent,
        language: templateLanguage,
        channelType: channelType,
        isPublished: true, // submit => true
        type: 'template',
      };

      if (id) {
        requestData.id = id;
      }

      console.log('Submitting template:', requestData);

      // 调用 API
      const response = await submitTemplate(requestData, basePath);

      console.log('Template submitted successfully:', response.data);

      // 提交成功后返回上一页
      setEditMode && setEditMode(false);
    } catch (error) {
      console.error('Failed to submit template:', error);
    }
  };

  // 处理保存（不发布）
  const handleSave = async () => {
    // 验证表单 - 和 Submit 一样的验证
    const errors = {
      templateName: !templateName || templateName.trim() === '',
      category: !category || category.trim() === '',
      templateType: !templateType || templateType.trim() === '',
      templateLanguage: !templateLanguage || templateLanguage.trim() === '',
    };

    setValidationErrors(errors);

    // 如果有验证错误，不执行保存
    if (
      errors.templateName ||
      errors.category ||
      errors.templateType ||
      errors.templateLanguage
    ) {
      return;
    }

    try {
      // 从 searchParams 获取必要的参数
      const spaceId = searchParams.get('spaceId') || '';
      const channelType = 'whatsapp';
      const parentId = searchParams.get('id') || '';
      const id = selectedTemplate?.id;

      // 构建模板内容
      const templateContent = JSON.stringify({
        components: components,
      });

      // 构建 API 请求数据
      const requestData: SubmitTemplateRequest = {
        name: templateName,
        spaceId: spaceId,
        parentId: parentId,
        content: templateContent,
        language: templateLanguage,
        channelType: channelType,
        isPublished: false, // save => false
        type: 'template',
      };

      if (id) {
        requestData.id = id;
      }

      console.log('Saving template:', requestData);

      // 调用 API
      const response = await submitTemplate(requestData, basePath);

      console.log('Template saved successfully:', response.data);

      // 保存成功后可以显示成功消息
      // 暂时返回上一页
      setEditMode && setEditMode(false);
    } catch (error) {
      console.error('Failed to save template:', error);
    }
  };

  // 清除特定字段的验证错误
  const clearValidationError = (field: keyof typeof validationErrors) => {
    if (validationErrors[field]) {
      setValidationErrors((prev) => ({
        ...prev,
        [field]: false,
      }));
    }
  };

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-y-auto">
      {/* 顶部导航和操作按钮 */}
      <section className="mt-4 flex flex-row items-center">
        <div className="flex-1 flex flex-row items-center">
          <ChevronLeft
            className="cursor-pointer"
            onClick={() => {
              setEditMode && setEditMode(false);
            }}
          />
          <span className="ml-2 text-sm text-gray-600">
            Canned Responses / Salutation
          </span>
        </div>
        {editable && (
          <div className="flex-1 flex flex-row items-center justify-end gap-x-2">
            <button
              className="px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors text-sm"
              onClick={() => setShowDiscardPopup(true)}
            >
              Discard
            </button>
            <button
              className="px-3 py-1 border border-gray-300 text-white bg-gray-800 rounded-md hover:bg-gray-700 transition-colors text-sm"
              onClick={handleSubmitClick}
            >
              Submit
            </button>
            <button
              className="px-3 py-1 border border-gray-300 text-white bg-gray-800 rounded-md hover:bg-gray-700 transition-colors text-sm"
              onClick={handleSave}
            >
              Save
            </button>
          </div>
        )}
      </section>

      {/* 主要内容区域 */}
      <section className="flex-1 mt-4 flex flex-row w-full gap-4">
        {/* 左侧表单区域 */}
        <div className="flex-1 flex flex-col gap-4">
          {/* Category */}
          <div>
            <label className="block text-sm font-medium mb-1">Category*</label>
            <div className="relative w-full">
              <select
                value={category}
                disabled={!editable || !allowCategoryChange}
                className={`bg-white border rounded px-2 py-1 w-full text-black text-sm leading-[1.17] focus:outline-none appearance-none pr-10 ${
                  validationErrors.category
                    ? 'border-red-500 focus:ring-2 focus:ring-red-500'
                    : 'border-[#DEDEDE] focus:ring-2 focus:ring-blue-500'
                } ${!editable || !allowCategoryChange ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                onChange={(e) => {
                  if (editable && allowCategoryChange) {
                    setCategory(e.target.value);
                    clearValidationError('category');
                  }
                }}
              >
                <option
                  value=""
                  disabled
                  className="text-gray-500"
                >
                  Select Category
                </option>
                <option value="Marketing">Marketing</option>
                <option value="Authentication">Authentication</option>
                <option value="Utility">Utility</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </div>
            </div>
            <div className="flex items-center mt-1">
              <input
                type="checkbox"
                id="allowCategoryChange"
                checked={allowCategoryChange}
                onChange={(e) => setAllowCategoryChange(e.target.checked)}
                className="mr-2"
              />
              <label
                htmlFor="allowCategoryChange"
                className="text-sm text-gray-600"
              >
                Allow Category Change
              </label>
            </div>
          </div>

          {/* Template Type */}
          <div>
            <label className="block text-sm font-medium mb-1">
              Template type*
            </label>
            <div className="relative w-full">
              <select
                value={templateType}
                disabled={!editable}
                className={`bg-white border rounded px-2 py-1 w-full text-black text-sm leading-[1.17] focus:outline-none appearance-none pr-10 ${
                  validationErrors.templateType
                    ? 'border-red-500 focus:ring-2 focus:ring-red-500'
                    : 'border-[#DEDEDE] focus:ring-2 focus:ring-blue-500'
                } ${!editable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                onChange={(e) => {
                  if (editable) {
                    setTemplateType(e.target.value);
                    clearValidationError('templateType');
                  }
                }}
              >
                <option
                  value=""
                  disabled
                  className="text-gray-500"
                >
                  Select Template Type
                </option>
                <option value="Media & Interactive">Media & Interactive</option>
                <option value="Text">Text</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </div>
            </div>
          </div>

          {/* Language */}
          <div>
            <label className="block text-sm font-medium mb-1">Language*</label>
            <div className="relative w-full">
              <select
                value={templateLanguage}
                disabled={!editable}
                className={`bg-white border rounded px-2 py-1 w-full text-black text-sm leading-[1.17] focus:outline-none appearance-none pr-10 ${
                  validationErrors.templateLanguage
                    ? 'border-red-500 focus:ring-2 focus:ring-red-500'
                    : 'border-[#DEDEDE] focus:ring-2 focus:ring-blue-500'
                } ${!editable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                onChange={(e) => {
                  if (editable) {
                    setTemplateLanguage(e.target.value);
                    clearValidationError('templateLanguage');
                  }
                }}
              >
                <option
                  value=""
                  disabled
                  className="text-gray-500"
                >
                  Select Language
                </option>
                <option value="en">English (en)</option>
                <option value="zh-CN">中文 (zh-CN)</option>
                <option value="zh-HK">繁體中文 (zh-HK)</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </div>
            </div>
          </div>

          {/* Template Name */}
          <div>
            <label className="block text-sm font-medium mb-1">
              Template Name
            </label>
            <input
              type="text"
              value={templateName}
              disabled={!editable}
              className={`w-full border rounded px-2 py-1 text-sm focus:outline-none ${
                validationErrors.templateName
                  ? 'border-red-500 focus:ring-2 focus:ring-red-500'
                  : 'border-[#DEDEDE] focus:ring-2 focus:ring-blue-500'
              } ${!editable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
              placeholder="Enter template name"
              onChange={(e) => {
                if (editable) {
                  setTemplateName(e.target.value);
                  clearValidationError('templateName');
                }
              }}
            />
          </div>

          {/* 模板构建区域 */}
          {templateLanguage && (
            <div className="mt-6">
              <label className="block text-sm font-medium mb-2">
                Select message template building blocks.
              </label>

              {/* 构建块选择器 */}
              <div className="flex gap-2 mb-4">
                <button
                  className={`px-3 py-1 text-sm rounded ${
                    selectedSection === 'Header'
                      ? 'bg-gray-800 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                  onClick={() => setSelectedSection('Header')}
                >
                  Header
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded ${
                    selectedSection === 'Body'
                      ? 'bg-gray-800 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                  onClick={() => setSelectedSection('Body')}
                >
                  Body
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded ${
                    selectedSection === 'Footer'
                      ? 'bg-gray-800 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                  onClick={() => setSelectedSection('Footer')}
                >
                  Footer
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded ${
                    selectedSection === 'Buttons'
                      ? 'bg-gray-800 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                  onClick={() => setSelectedSection('Buttons')}
                >
                  Buttons
                </button>
              </div>

              {/* Header 部分 */}
              {selectedSection === 'Header' && (
                <div className="border border-gray-200 rounded p-4">
                  <h3 className="font-medium mb-2">HEADER</h3>
                  <div className="mb-2">
                    <select
                      className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                      value={
                        components.find((c) => c.type === 'HEADER')?.format ||
                        ''
                      }
                      onChange={(e) => {
                        const headerComponent = components.find(
                          (c) => c.type === 'HEADER'
                        );
                        if (headerComponent) {
                          updateComponent(
                            headerComponent.id,
                            'format',
                            e.target.value
                          );
                        } else {
                          setComponents((prev) => [
                            ...prev,
                            {
                              id: 'header-1',
                              type: 'HEADER',
                              format: e.target.value as 'TEXT' | 'IMAGE',
                              text: '',
                            },
                          ]);
                        }
                      }}
                    >
                      <option value="">Select format</option>
                      <option value="IMAGE">Image</option>
                      <option value="TEXT">Text</option>
                    </select>
                  </div>

                  {components.find((c) => c.type === 'HEADER')?.format ===
                    'IMAGE' && (
                    <div className="bg-yellow-100 p-3 rounded">
                      <div className="text-center text-gray-600">
                        <div className="w-16 h-16 bg-gray-300 rounded mx-auto mb-2 flex items-center justify-center">
                          📷
                        </div>
                        <p className="text-sm">Image</p>
                      </div>
                    </div>
                  )}

                  {components.find((c) => c.type === 'HEADER')?.format ===
                    'TEXT' && (
                    <div>
                      <label className="block text-sm mb-1">Text</label>
                      <textarea
                        className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                        rows={2}
                        placeholder="Enter header text"
                        value={
                          components.find((c) => c.type === 'HEADER')?.text ||
                          ''
                        }
                        onChange={(e) => {
                          const headerComponent = components.find(
                            (c) => c.type === 'HEADER'
                          );
                          if (headerComponent) {
                            updateComponent(
                              headerComponent.id,
                              'text',
                              e.target.value
                            );
                          }
                        }}
                      />
                    </div>
                  )}
                </div>
              )}

              {/* Body 部分 */}
              {selectedSection === 'Body' && (
                <div className="border border-gray-200 rounded p-4">
                  <h3 className="font-medium mb-2">BODY*</h3>
                  <div className="flex items-center gap-2 mb-2">
                    <button className="px-2 py-1 bg-gray-200 rounded text-sm">
                      B
                    </button>
                    <button className="px-2 py-1 bg-gray-200 rounded text-sm">
                      I
                    </button>
                    <button className="px-2 py-1 bg-gray-200 rounded text-sm">
                      U
                    </button>
                    <button className="px-2 py-1 bg-gray-200 rounded text-sm">
                      Add variable
                    </button>
                  </div>
                  <textarea
                    className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                    rows={4}
                    placeholder="This code expires in {{1}} MINUTES/minutes."
                    value={
                      components.find((c) => c.type === 'BODY')?.text || ''
                    }
                    onChange={(e) => {
                      const bodyComponent = components.find(
                        (c) => c.type === 'BODY'
                      );
                      if (bodyComponent) {
                        updateComponent(
                          bodyComponent.id,
                          'text',
                          e.target.value
                        );
                      } else {
                        setComponents((prev) => [
                          ...prev,
                          {
                            id: 'body-1',
                            type: 'BODY',
                            text: e.target.value,
                          },
                        ]);
                      }
                    }}
                  />
                  <div className="text-xs text-gray-500 mt-1">1/60</div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 右侧预览区域 */}
        <div className="flex-1 flex flex-col min-h-[500px] border border-[#DEDEDE] rounded bg-white">
          <h3 className="text-sm font-bold text-black p-3 pb-0">Preview</h3>
          <div className="flex-1 bg-[#FFF5DA] m-3 mt-3 rounded p-[14px]">
            <div className="bg-white rounded-lg p-4 max-w-sm mx-auto">
              {/* 预览内容 */}
              {components.find((c) => c.type === 'HEADER')?.format ===
                'IMAGE' && (
                <div className="mb-3">
                  <div className="w-full h-32 bg-gray-200 rounded flex items-center justify-center">
                    <span className="text-gray-500">📷 Image placeholder</span>
                  </div>
                </div>
              )}

              {components.find((c) => c.type === 'HEADER')?.format ===
                'TEXT' && (
                <div className="mb-3 font-medium">
                  {components.find((c) => c.type === 'HEADER')?.text ||
                    'Header text'}
                </div>
              )}

              <div className="mb-3 text-sm leading-[1.3]">
                {components.find((c) => c.type === 'BODY')?.text ||
                  'This code expires in {{1}} MINUTES/minutes.'}
              </div>

              {components.find((c) => c.type === 'FOOTER')?.text && (
                <div className="text-xs text-gray-500 mb-3">
                  {components.find((c) => c.type === 'FOOTER')?.text}
                </div>
              )}

              <div className="space-y-2">
                <div className="bg-gray-100 rounded p-2 text-center text-sm">
                  Flow
                </div>
                <div className="bg-gray-100 rounded p-2 text-center text-sm">
                  Use existing
                </div>
                <div className="bg-gray-100 rounded p-2 text-center text-sm">
                  Url
                </div>
                <div className="bg-gray-100 rounded p-2 text-center text-sm">
                  Phone number
                </div>
                <div className="bg-gray-100 rounded p-2 text-center text-sm">
                  Quick reply
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Discard Confirmation Popup */}
      <ConfirmationPopup
        isOpen={showDiscardPopup}
        onClose={() => setShowDiscardPopup(false)}
        onConfirm={handleDiscardConfirm}
        message="Are you sure you want to discard changes?"
        confirmText="Yes"
        cancelText="No"
      />

      {/* Submit Confirmation Popup */}
      <ConfirmationPopup
        isOpen={showSubmitPopup}
        onClose={() => setShowSubmitPopup(false)}
        onConfirm={handleSubmitConfirm}
        message="Are you sure you want to submit this template?"
        confirmText="Yes"
        cancelText="No"
      />
    </div>
  );
};

export default WhatsAppTemplateDetail;
